{"/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/a4c6aac8b18c180d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/a4c6aac8b18c180d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/TransportSecurity": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "TransportSecurity"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/91baa182c7a11977_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/91baa182c7a11977_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/91baa182c7a11977_1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/91baa182c7a11977_1"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4d89adc913736ca2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4d89adc913736ca2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/22ba9f02cfebaa9f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/22ba9f02cfebaa9f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a90e3fb8d904d5dc_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a90e3fb8d904d5dc_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cookies-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cookies-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/Network Persistent State": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Network Persistent State"}, "/Users/<USER>/Library/Application Support/Windsurf/Preferences": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Preferences"}, "/Users/<USER>/Library/Application Support/Windsurf/REPORT.md": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "REPORT.md"}, "/Users/<USER>/Library/Application Support/Windsurf/SharedStorage": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "SharedStorage"}, "/Users/<USER>/Library/Application Support/Windsurf/Trust Tokens-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Trust Tokens-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/code.lock": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "code.lock"}, "/Users/<USER>/Library/Application Support/Windsurf/languagepacks.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "languagepacks.json"}, "/Users/<USER>/Library/Application Support/Windsurf/machineid": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "machineid"}, "/Users/<USER>/Library/Application Support/Windsurf/reset_windsurf_identifiers.sh": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "reset_windsurf_identifiers.sh"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/output_20250613T230041/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/output_20250613T230041/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/output_logging_20250613T230042/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/output_logging_20250613T230042/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/output_logging_20250613T230042/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/output_logging_20250613T230042/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/output_20250613T225616/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/output_20250613T225616/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.json-language-features/JSON Language Server.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/output_logging_20250613T225616/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/output_logging_20250613T225616/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/QuotaManager-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/QuotaManager-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/User/keybindings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/keybindings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json.backup.20250613_225051": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json.backup.20250613_225051"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json.backup.20250613_225402": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json.backup.20250613_225402"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json.backup.20250613_225551": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json.backup.20250613_225551"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/workspace.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/workspace.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/Augment-Memories": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/Augment-Memories"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/25dd58e4-b424-4c48-9641-042a10254e92": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/25dd58e4-b424-4c48-9641-042a10254e92"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/387b9221-ef76-4360-97ea-9642fccc0a22": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/387b9221-ef76-4360-97ea-9642fccc0a22"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/489af9ee-a70b-4184-810d-9baadcbd5fd7": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/489af9ee-a70b-4184-810d-9baadcbd5fd7"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/storage.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/storage.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Shared Dictionary/db-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Shared Dictionary/db-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/output_20250614T002904/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/output_20250614T002904/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/output_logging_20250614T002904/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/output_logging_20250614T002904/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/output_logging_20250614T002904/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/output_logging_20250614T002904/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749826847458.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749826847458.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedProfilesData/__default__profile__/extensions.user.cache": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedProfilesData/__default__profile__/extensions.user.cache"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/output_20250614T074655/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/output_20250614T074655/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/output_logging_20250614T074656/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/output_logging_20250614T074656/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/output_logging_20250614T074656/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/output_logging_20250614T074656/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749832152252.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749832152252.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3481cc13e215ffd2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3481cc13e215ffd2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/72f7112d083f0dee_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/72f7112d083f0dee_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f7dad7deee8c8f7d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f7dad7deee8c8f7d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3149e4769993e281_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3149e4769993e281_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f7dbf2ffd2184d14_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f7dbf2ffd2184d14_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ac40e91be8dd6e38_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ac40e91be8dd6e38_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1d0260b4b01bad00_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1d0260b4b01bad00_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ad1f28be1c969e99_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ad1f28be1c969e99_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9a59e90454e9c708_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9a59e90454e9c708_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/bdfed1d573e5711e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/bdfed1d573e5711e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a1af4c22e2675098_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a1af4c22e2675098_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/63e27d0f0c69839f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/63e27d0f0c69839f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3659d367f384af9b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3659d367f384af9b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/aa5549a8342508ed_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/aa5549a8342508ed_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9ea70d06c4106fcf_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9ea70d06c4106fcf_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7a21949f78a9aac7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7a21949f78a9aac7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2107cc3d6502d201_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2107cc3d6502d201_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f4f4fe0cd05bfea7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f4f4fe0cd05bfea7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d75d4dc96def1d1c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d75d4dc96def1d1c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9ec6965efc9b1606_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9ec6965efc9b1606_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/cb52ace8e6bd52e9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/cb52ace8e6bd52e9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d0de2e3fb4a5bdb0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d0de2e3fb4a5bdb0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/39df776b3f13524a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/39df776b3f13524a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/cee6a450d2d90326_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/cee6a450d2d90326_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2ee99bfdd664d76c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2ee99bfdd664d76c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3218ef3ecac63bff_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3218ef3ecac63bff_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6be3d6ae6e38c2bb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6be3d6ae6e38c2bb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d86517e97d5afb32_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d86517e97d5afb32_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1da97ab4605e9f6b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1da97ab4605e9f6b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3a768d4ed79c1406_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3a768d4ed79c1406_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/df5d6f001d1a17ee_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/df5d6f001d1a17ee_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1d6865f4e7aa5978_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1d6865f4e7aa5978_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/fd88aba0da6459f2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/fd88aba0da6459f2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9d5e6ac9b2ab1a9c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9d5e6ac9b2ab1a9c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/38244dde21f4451b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/38244dde21f4451b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7595612a25ca6f59_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7595612a25ca6f59_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/fa74e8fa1b70ec04_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/fa74e8fa1b70ec04_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/dff6c42a671637c8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/dff6c42a671637c8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/879fdb5c32fe9413_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/879fdb5c32fe9413_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e1af54c63a6d6c38_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e1af54c63a6d6c38_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d714624dbb616b8d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d714624dbb616b8d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/68d31696b7c471b3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/68d31696b7c471b3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/89aef29384f00e5e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/89aef29384f00e5e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a15a1688829e0b72_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a15a1688829e0b72_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4a7ad2f35ca9ae26_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4a7ad2f35ca9ae26_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/61799e8ad071d3f0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/61799e8ad071d3f0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/46929ec54186759a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/46929ec54186759a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d141cd22c7f08270_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d141cd22c7f08270_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7fe272bad6f0f5f3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7fe272bad6f0f5f3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4582487e83b4f3a4_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4582487e83b4f3a4_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3bd1de697b42425c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3bd1de697b42425c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/eb5f9d5a8f62f350_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/eb5f9d5a8f62f350_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f8f23be1d8900ad6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f8f23be1d8900ad6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3411d4ec6ae2518d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3411d4ec6ae2518d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6c5ef97a9f037874_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6c5ef97a9f037874_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/47a90a84067e50fb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/47a90a84067e50fb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d176b6af2e97209c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d176b6af2e97209c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/667a6ead8df85f5a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/667a6ead8df85f5a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9ea9ace88596567b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9ea9ace88596567b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9f50bddbd978eddd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9f50bddbd978eddd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/eb7a734da8a37aa2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/eb7a734da8a37aa2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/df6fcd0437a1f82c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/df6fcd0437a1f82c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1a047c63d436fbc7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1a047c63d436fbc7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a45665e025baeaf7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a45665e025baeaf7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/25cb86e9ed349358_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/25cb86e9ed349358_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e5093158811c76d4_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e5093158811c76d4_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/10b425914808b0fd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/10b425914808b0fd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1885287a7c9c934a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1885287a7c9c934a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/65d9af677bb7dfba_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/65d9af677bb7dfba_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/148e517948b45d20_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/148e517948b45d20_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/66a491ea53586782_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/66a491ea53586782_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d816c2a08bf7c07d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d816c2a08bf7c07d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c9a56df6538e20d2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c9a56df6538e20d2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/8cb93ccd87858690_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/8cb93ccd87858690_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2b751e3d7cdaddbb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2b751e3d7cdaddbb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/65f83456c0b85a8c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/65f83456c0b85a8c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3b6a9cf5e47c4630_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3b6a9cf5e47c4630_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/add1c8f8870e6fd3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/add1c8f8870e6fd3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f033ac2a754e11dc_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f033ac2a754e11dc_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/5618f40db2970022_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/5618f40db2970022_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/0a5f1a3d0024768d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/0a5f1a3d0024768d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1768bc79de2c5780_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1768bc79de2c5780_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e9749e9530d4193f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e9749e9530d4193f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b4aad5b8336281ed_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b4aad5b8336281ed_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/87f288ac6520d59d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/87f288ac6520d59d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9056de3ec1d2d468_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9056de3ec1d2d468_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f5796e72e2cce43e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f5796e72e2cce43e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/de1fec7a6e175d70_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/de1fec7a6e175d70_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/dc03a19f280a8946_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/dc03a19f280a8946_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c84d8f7ec435c939_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c84d8f7ec435c939_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b7328d07b272a48d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b7328d07b272a48d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d92e50f3c6146d73_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d92e50f3c6146d73_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9c0d1c026a37e0d0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9c0d1c026a37e0d0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/95c6895554e51aac_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/95c6895554e51aac_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6ce3c3744aa1d153_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6ce3c3744aa1d153_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a7a6b741258ab8ac_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a7a6b741258ab8ac_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/895ef8471aa1bc9c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/895ef8471aa1bc9c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4835889149fb804d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4835889149fb804d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7537b24013e1d860_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7537b24013e1d860_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6a322e352e7b81ec_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6a322e352e7b81ec_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4a0933e0e0971a06_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4a0933e0e0971a06_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e32b73e5913725ea_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e32b73e5913725ea_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1f19e8e5ce2205b8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1f19e8e5ce2205b8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d460bf91cd8fa070_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d460bf91cd8fa070_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b444f5199c02b9fa_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b444f5199c02b9fa_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/8c0f614cd661418d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/8c0f614cd661418d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/091189931297e767_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/091189931297e767_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ae6dd3c9cd351556_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ae6dd3c9cd351556_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/359f086fed4dcd48_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/359f086fed4dcd48_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/dde081bb84d2d6f7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/dde081bb84d2d6f7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/098978814ebcb67b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/098978814ebcb67b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c56926febb7dda4c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c56926febb7dda4c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e2579b5808afc90c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e2579b5808afc90c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a9efa1373eef6e76_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a9efa1373eef6e76_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/367e0f25d86e690d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/367e0f25d86e690d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f0ef2539907dbe9d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f0ef2539907dbe9d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c3803b2aabdcdd11_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c3803b2aabdcdd11_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9772c86db7f43c10_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9772c86db7f43c10_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/69844cdb143c94a5_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/69844cdb143c94a5_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a65ec934fa5caf43_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a65ec934fa5caf43_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/149b659e97c8e3d5_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/149b659e97c8e3d5_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/53d10518de533f8d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/53d10518de533f8d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4b4f08ff42b54015_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4b4f08ff42b54015_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/06b7c1600800d144_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/06b7c1600800d144_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6c23e2e8ef9af7bd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6c23e2e8ef9af7bd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2ae5eaa6b83cb8b3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2ae5eaa6b83cb8b3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/34facd7d904a22df_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/34facd7d904a22df_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000180.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000180.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2e0bc91320df1e3f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2e0bc91320df1e3f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/008da93c-09b9-4dfe-9547-bb758edab4e8": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/008da93c-09b9-4dfe-9547-bb758edab4e8"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a623069658b0672e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a623069658b0672e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000182.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000182.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/22b0592d8815ca62_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/22b0592d8815ca62_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/88d5d97666d9153e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/88d5d97666d9153e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000184.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000184.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a3c162103963a9f6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a3c162103963a9f6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/93d85e0dfd07b66a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/93d85e0dfd07b66a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/b3bd0d67f800d222_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/b3bd0d67f800d222_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/b3bd0d67f800d222_1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/b3bd0d67f800d222_1"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/output_20250614T112831/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/output_20250614T112831/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/output_logging_20250614T112831/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/output_logging_20250614T112831/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/output_logging_20250614T112831/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/output_logging_20250614T112831/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749858424143.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749858424143.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/468b8200d7ed5e7d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/468b8200d7ed5e7d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a12249670ba7d541_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a12249670ba7d541_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/bfdc14f0-42ca-40c4-ab27-38d4e700331a": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/bfdc14f0-42ca-40c4-ab27-38d4e700331a"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1757a7b8c2641473_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1757a7b8c2641473_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000188.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000188.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000190.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000190.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/SharedStorage-wal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "SharedStorage-wal"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/252840453ae0f258_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/252840453ae0f258_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ee67a1d4316a3442_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ee67a1d4316a3442_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000191.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000191.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000192.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000192.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e503009367b3e554_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e503009367b3e554_0"}, "/Users/<USER>/Library/Application Support/Windsurf/文件同步方案.md": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "文件同步方案.md"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/9ec96f4f02f74c83_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/9ec96f4f02f74c83_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/f8b50749833d79e7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/f8b50749833d79e7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-19905dea-6314-43ae-b0e5-00ca9c1e14c1.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-19905dea-6314-43ae-b0e5-00ca9c1e14c1.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872050513-242cc9b9-c43d-4950-8d0e-67c5e521b1de.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872050513-242cc9b9-c43d-4950-8d0e-67c5e521b1de.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7d90c12404dbee48_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7d90c12404dbee48_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000193.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000193.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000194.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000194.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/mac_realtime_sync.sh": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/mac_realtime_sync.sh"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_mac_realtime_sync.sh-1749872078745-2f452926-82b5-4dfb-b821-c47209be7f9d.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_mac_realtime_sync.sh-1749872078745-2f452926-82b5-4dfb-b821-c47209be7f9d.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/cbf3a8e15a7c5d30_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/cbf3a8e15a7c5d30_0"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/windows_realtime_sync.ps1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/windows_realtime_sync.ps1"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_windows_realtime_sync.ps1-1749872122192-bc9947cd-a1cd-47c0-a22a-122b0abee5b2.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_windows_realtime_sync.ps1-1749872122192-bc9947cd-a1cd-47c0-a22a-122b0abee5b2.json"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/sync_config.conf": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/sync_config.conf"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.conf-1749872133278-c2b8cefb-efd9-4ade-823e-3e9edc9b81c6.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.conf-1749872133278-c2b8cefb-efd9-4ade-823e-3e9edc9b81c6.json"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/sync_config.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/sync_config.json"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/vscode.json-language-features/JSON Language Server.log"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.json-1749872143980-f7b50a7b-5ccc-45aa-9b6d-121cbcc982e4.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.json-1749872143980-f7b50a7b-5ccc-45aa-9b6d-121cbcc982e4.json"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/exclude.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/exclude.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_exclude.txt-1749872156493-c94d82e7-aee3-4398-a044-ac5f5829c030.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_exclude.txt-1749872156493-c94d82e7-aee3-4398-a044-ac5f5829c030.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ef1311272ecedddd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ef1311272ecedddd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000195.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000195.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000196.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000196.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/setup.sh": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/setup.sh"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup.sh-1749872192535-61abaa61-6fc9-4d37-8727-6360e68ea2a2.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup.sh-1749872192535-61abaa61-6fc9-4d37-8727-6360e68ea2a2.json"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/README.md": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/README.md"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_README.md-1749872231847-f40844db-eec0-4851-9e35-34ed443a3b6e.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_README.md-1749872231847-f40844db-eec0-4851-9e35-34ed443a3b6e.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/87f726da1f66ea4b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/87f726da1f66ea4b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/storage.json.backup_2025-06-14T03_37_40_586Z": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/storage.json.backup_2025-06-14T03_37_40_586Z"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/setup_windows.ps1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/setup_windows.ps1"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup_windows.ps1-1749872290290-41e99ec4-356f-47ed-b9fb-0aad6997cab3.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup_windows.ps1-1749872290290-41e99ec4-356f-47ed-b9fb-0aad6997cab3.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f7f1eb128fc793b7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f7f1eb128fc793b7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000197.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000197.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000199.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000199.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/52bbe073cb91d2f9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/52bbe073cb91d2f9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/9487d3e439c3a17f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/9487d3e439c3a17f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/9be633f5983b4a04_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/9be633f5983b4a04_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/index.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/index.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T112829/window1/exthost/vscode.markdown-language-features/Markdown.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T112829/window1/exthost/vscode.markdown-language-features/Markdown.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/bb62d54d30da6582_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/bb62d54d30da6582_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/5a90c956cf95a91c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/5a90c956cf95a91c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/f05a5bc0baa79cdf_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/f05a5bc0baa79cdf_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7a001368dd6ffb76_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7a001368dd6ffb76_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000200.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000200.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000201.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000201.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/6658daa1/8qcX.md": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/6658daa1/8qcX.md"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872455099-848ddf64-f45d-4fd6-8693-1234bade1efb.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872455099-848ddf64-f45d-4fd6-8693-1234bade1efb.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872455112-0e0f71bc-9c26-41e6-ba33-bed06a3ae926.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872455112-0e0f71bc-9c26-41e6-ba33-bed06a3ae926.json"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/speed_optimized_config.conf": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/speed_optimized_config.conf"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/57d2afba6e80ce2a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/57d2afba6e80ce2a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/6658daa1/Uqfi.md": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/6658daa1/Uqfi.md"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_README.md-1749872477072-81b36450-4b03-4685-a035-90fd2f5259ae.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_README.md-1749872477072-81b36450-4b03-4685-a035-90fd2f5259ae.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_exclude.txt-1749872477072-0809a911-229a-4716-937c-ba71e817362d.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_exclude.txt-1749872477072-0809a911-229a-4716-937c-ba71e817362d.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_mac_realtime_sync.sh-1749872477072-5c86b6c4-693d-4437-8463-f5660db24f00.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_mac_realtime_sync.sh-1749872477072-5c86b6c4-693d-4437-8463-f5660db24f00.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup.sh-1749872477072-1e4f13cb-2d36-499e-901c-b2258b4627ca.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup.sh-1749872477072-1e4f13cb-2d36-499e-901c-b2258b4627ca.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup_windows.ps1-1749872477072-d175e43e-5860-459d-a5a4-557a0430035d.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_setup_windows.ps1-1749872477072-d175e43e-5860-459d-a5a4-557a0430035d.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_optimized_config.conf-1749872475742-e448a2db-4f9d-43d3-97ed-f04d0cb5b1e9.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_optimized_config.conf-1749872475742-e448a2db-4f9d-43d3-97ed-f04d0cb5b1e9.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_optimized_config.conf-1749872477072-0db20bdc-1cec-49b4-a1cc-129d6ae5a67f.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_optimized_config.conf-1749872477072-0db20bdc-1cec-49b4-a1cc-129d6ae5a67f.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_optimized_config.conf-1749872477254-24564c89-2829-49f0-9407-135aaf020b96.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_optimized_config.conf-1749872477254-24564c89-2829-49f0-9407-135aaf020b96.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.conf-1749872477072-a6e4f1ae-b531-4646-90ea-b8bbf7603ddb.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.conf-1749872477072-a6e4f1ae-b531-4646-90ea-b8bbf7603ddb.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.json-1749872477072-428ec334-82cc-4618-9239-0cce00145367.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_sync_config.json-1749872477072-428ec334-82cc-4618-9239-0cce00145367.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_windows_realtime_sync.ps1-1749872477072-2b4a8b64-f207-4c7a-92b2-fefe64cca802.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_windows_realtime_sync.ps1-1749872477072-2b4a8b64-f207-4c7a-92b2-fefe64cca802.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872477072-d9c47f60-8725-47f9-931e-ebd0d16f0810.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872477072-d9c47f60-8725-47f9-931e-ebd0d16f0810.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872477081-c9c7a976-69a3-4237-8bfa-222f752e7f2f.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_文件同步方案.md-1749872477081-c9c7a976-69a3-4237-8bfa-222f752e7f2f.json"}, "/Users/<USER>/Library/Application Support/Windsurf/sync_scripts/speed_test.sh": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "sync_scripts/speed_test.sh"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_test.sh-1749872516250-8815a6c4-ed28-4a60-b335-b38ef0934a8b.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_sync_scripts_speed_test.sh-1749872516250-8815a6c4-ed28-4a60-b335-b38ef0934a8b.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/82f5d11637a103cb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/82f5d11637a103cb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7730c5ea8c432774_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7730c5ea8c432774_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/906f0c5bd25acca9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/906f0c5bd25acca9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000202.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000202.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000203.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000203.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/unity_cloud_dev/README.md": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "unity_cloud_dev/README.md"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_unity_cloud_dev_README.md-1749872567647-7097b1f6-f476-471d-b7c4-720194478560.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_unity_cloud_dev_README.md-1749872567647-7097b1f6-f476-471d-b7c4-720194478560.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d48c1909ac2b50cd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d48c1909ac2b50cd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/.com.exafunction.windsurf.ylylWR": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": ".com.exafunction.windsurf.ylylWR"}, "/Users/<USER>/Library/Application Support/Windsurf/unity_cloud_dev/setup_unity_cloud.sh": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "unity_cloud_dev/setup_unity_cloud.sh"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_unity_cloud_dev_setup_unity_cloud.sh-1749872623384-011c0da1-c3cd-4561-9689-6eebb56eb11a.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19905dea-6314-43ae-b0e5-00ca9c1e14c1/document-_Users_denesblack_Library_Application Support_Windsurf_unity_cloud_dev_setup_unity_cloud.sh-1749872623384-011c0da1-c3cd-4561-9689-6eebb56eb11a.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/4f59ab45/Q9eG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/4f59ab45/Q9eG"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3ce0dcd6dfb1c0c9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3ce0dcd6dfb1c0c9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000204.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000204.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000205.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000205.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/state.vscdb-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/state.vscdb-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/30fa69e157c2fe1d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/30fa69e157c2fe1d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/4f59ab45/entries.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/4f59ab45/entries.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/6658daa1/entries.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/6658daa1/entries.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ab6a2f449f9637ae_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ab6a2f449f9637ae_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000206.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000206.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000208.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000208.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a28df3eea9a5063a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a28df3eea9a5063a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a1ec83bcd7e808dc_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a1ec83bcd7e808dc_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000209.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000209.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000210.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000210.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/67d8c00f2c205c10_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/67d8c00f2c205c10_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/47675680f9d5129d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/47675680f9d5129d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000211.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000211.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000212.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000212.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/24614f12b50b7cb2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/24614f12b50b7cb2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/4f59ab45/zNDc": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/4f59ab45/zNDc"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a2b00901e28a5760_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a2b00901e28a5760_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1e96f93c20f22929_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1e96f93c20f22929_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7fb5c5594ae35468_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7fb5c5594ae35468_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/49ce27f18f66531c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/49ce27f18f66531c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/70aa99ab777617ef_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/70aa99ab777617ef_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/c3a7e8f85b9e6506_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/c3a7e8f85b9e6506_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d9c7a5d71eac98c1_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d9c7a5d71eac98c1_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000213.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000213.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000214.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000214.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/4f59ab45/1JFO": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/4f59ab45/1JFO"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/711eeaa9185d9437_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/711eeaa9185d9437_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6d8563e02cfe82cf_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6d8563e02cfe82cf_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000215.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000215.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000217.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000217.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/041797d164ad77c9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/041797d164ad77c9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/5fdc891113576046_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/5fdc891113576046_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b7e02d2cf43f6494_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b7e02d2cf43f6494_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/bafeaccf762f59570a908d43e8993df6bc34e0a84199911e3d7266b1151f9569.png": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/bafeaccf762f59570a908d43e8993df6bc34e0a84199911e3d7266b1151f9569.png"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/144d0dd50b584e52_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/144d0dd50b584e52_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000218.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000218.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000219.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000219.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/.com.exafunction.windsurf.oYio06": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": ".com.exafunction.windsurf.oYio06"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/719e2fb62df9173f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/719e2fb62df9173f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/90288ab737adb900_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/90288ab737adb900_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/2f93daf46090c2f5_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/2f93daf46090c2f5_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/01049b641e7ca22f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/01049b641e7ca22f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000220.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000220.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000221.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000221.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c6212031390873ee_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c6212031390873ee_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/d0a1ace191eef00d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/d0a1ace191eef00d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b9c60878f19a1902_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b9c60878f19a1902_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6c45c6da45c7de32_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6c45c6da45c7de32_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000222.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000222.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000223.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000223.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/4f59ab45/QKHq": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/4f59ab45/QKHq"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d4b9972db629afef_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d4b9972db629afef_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/6331cf6712dd96fc_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/6331cf6712dd96fc_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f3b8ce6566807f04_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f3b8ce6566807f04_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/4f59ab45/QjTq": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/4f59ab45/QjTq"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1c756f2194ca8d80_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1c756f2194ca8d80_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000224.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000224.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000226.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000226.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e668206551a58620_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e668206551a58620_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c27543acbcfa27a3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c27543acbcfa27a3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000227.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000227.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000228.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000228.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/36972f8e172b4c03_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/36972f8e172b4c03_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b5bdb7ae7386e680_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b5bdb7ae7386e680_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000229.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000229.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000230.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000230.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/f6777436af1816844bb93f5705acdffd37c2e5ef14a6e2eff5803982571feb2c.png": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/f6777436af1816844bb93f5705acdffd37c2e5ef14a6e2eff5803982571feb2c.png"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/aee2a458990f480b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/aee2a458990f480b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/0aee372a2f1377ea_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/0aee372a2f1377ea_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ccfd9ca538189156_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ccfd9ca538189156_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/642dfad474e1acc2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/642dfad474e1acc2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b005737dd33310e8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b005737dd33310e8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b4db9628f1b8bc10_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b4db9628f1b8bc10_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000231.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000231.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000232.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000232.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d3cab23219fa74c6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d3cab23219fa74c6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f07b0614a0efbfaa_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f07b0614a0efbfaa_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000233.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000233.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000234.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000234.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000235.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000235.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/92fc7499d0f01c3fc5ab0281469d146fd6af24ddbe139062f6b18e325271b225.png": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/92fc7499d0f01c3fc5ab0281469d146fd6af24ddbe139062f6b18e325271b225.png"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/511d71977ef20432_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/511d71977ef20432_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/3618f140f3cb279c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/3618f140f3cb279c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/4a35d83e21727218_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/4a35d83e21727218_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ced129f40b71b80f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ced129f40b71b80f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7b7b11aceca3f259_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7b7b11aceca3f259_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000236.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000236.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000237.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000237.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/199f210c5c6b7446_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/199f210c5c6b7446_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/60abf96a34046686_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/60abf96a34046686_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000238.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000238.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000239.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000239.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/5ad20e7e40c839b4_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/5ad20e7e40c839b4_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/5cea6bf8b3ba830d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/5cea6bf8b3ba830d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000240.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000240.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000241.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000241.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/28cf4aaf26f7ca84_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/28cf4aaf26f7ca84_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/8d74885f8a673ea8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/8d74885f8a673ea8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000242.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000242.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000244.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000244.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/19068054bb72502c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/19068054bb72502c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6152feac27609b66_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6152feac27609b66_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000245.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000245.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000246.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000246.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/f35dd52f75db4af9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/f35dd52f75db4af9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a9ef2d13a2fd8940_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a9ef2d13a2fd8940_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/96a46ab408596635_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/96a46ab408596635_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000247.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000247.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000248.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000248.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/53d2784642ce3af3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/53d2784642ce3af3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/0b265e8f1e50fcf1_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/0b265e8f1e50fcf1_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000249.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000249.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000250.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000250.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/59d3ec5ee2aba97e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/59d3ec5ee2aba97e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/aaef7e1198800ecb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/aaef7e1198800ecb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000251.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000251.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000253.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000253.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2e0dbcc66434c597_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2e0dbcc66434c597_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/caeed874960476d3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/caeed874960476d3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000254.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000254.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000255.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000255.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e953358ce99a22b1_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e953358ce99a22b1_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f0fd83857ee93378_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f0fd83857ee93378_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000256.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000256.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000257.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000257.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/42de1ef8dd69f64e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/42de1ef8dd69f64e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/267ae3406ba0e519_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/267ae3406ba0e519_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000258.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000258.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000259.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000259.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/63c75a030fc2feaf_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/63c75a030fc2feaf_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/41853775918fbfd6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/41853775918fbfd6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000260.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000260.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000262.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000262.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/14e8680e5198bd3a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/14e8680e5198bd3a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6bdb2ca750073bda_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6bdb2ca750073bda_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000263.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000263.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000264.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000264.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e297308119e48ced_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e297308119e48ced_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b343d3b6de30e1d3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b343d3b6de30e1d3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000265.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000265.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000266.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000266.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/0162cb5f06583009_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/0162cb5f06583009_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a15c465501413333_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a15c465501413333_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000267.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000267.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000268.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000268.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/989a0ab7657e2495_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/989a0ab7657e2495_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/51dc16fab04f0614_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/51dc16fab04f0614_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000269.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000269.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000271.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000271.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/47db40b4e188a189_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/47db40b4e188a189_0"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c7fa7f34-901a-43ae-bda3-3e54bd5fda31": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c7fa7f34-901a-43ae-bda3-3e54bd5fda31"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/245eb09d2629d76bb2ca8a4980f6dbb361d0dc250af01cd35814b24637cbca3d.png": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/245eb09d2629d76bb2ca8a4980f6dbb361d0dc250af01cd35814b24637cbca3d.png"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/af7b434d08d6ecd7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/af7b434d08d6ecd7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000272.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000272.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000273.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000273.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/502024bdd0c600d6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/502024bdd0c600d6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/afba9a31a8600c6d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/afba9a31a8600c6d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000274.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000274.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000275.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000275.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/History/4f59ab45/dJ2X": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/History/4f59ab45/dJ2X"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a31b7548d3e8bb36_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a31b7548d3e8bb36_0"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/04034edee03f8bd8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/04034edee03f8bd8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e32e4719600fa905_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e32e4719600fa905_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b7d756da0b67b539_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b7d756da0b67b539_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000276.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000276.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000277.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000277.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6c920a47b36cd027_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6c920a47b36cd027_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/366233512a253ec9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/366233512a253ec9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000278.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000278.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000280.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000280.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e28c0bb5eb24014a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e28c0bb5eb24014a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d2307c560d7614e1_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d2307c560d7614e1_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000281.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000281.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000282.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000282.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/764b12d70a0783d9003d6da7d837d75b492a802ee43968893b0e92d055378176.png": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/764b12d70a0783d9003d6da7d837d75b492a802ee43968893b0e92d055378176.png"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3eb962cae88f20ad_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3eb962cae88f20ad_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3a346951bdb621f1_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3a346951bdb621f1_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000283.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000283.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000284.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000284.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7bc5e7e7aa0fe73e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7bc5e7e7aa0fe73e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/973c059daf2e2bcd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/973c059daf2e2bcd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000285.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000285.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000286.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000286.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f80a37f86e4090a7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f80a37f86e4090a7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/52b7fee1a1461629_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/52b7fee1a1461629_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000287.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000287.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000289.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000289.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/70b8bf2aab58b01a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/70b8bf2aab58b01a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/93f93cb29afe0a85_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/93f93cb29afe0a85_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000290.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000290.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000291.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000291.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f2ade57d7cbe14b9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f2ade57d7cbe14b9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d411656e8bcf0761_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d411656e8bcf0761_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000292.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000292.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000293.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000293.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/05ac771fbf130a1c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/05ac771fbf130a1c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c2a3952cfb2c0b31_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c2a3952cfb2c0b31_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000294.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000294.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000295.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000295.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a8df6df4ce9b92cd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a8df6df4ce9b92cd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/26b8ba7c3e0fae54_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/26b8ba7c3e0fae54_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000296.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000296.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000297.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000297.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000298.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000298.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ba851a6269fd0c82_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ba851a6269fd0c82_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/428ad148754eb38f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/428ad148754eb38f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000299.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000299.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000300.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000300.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/700017e58a7ee39e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/700017e58a7ee39e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/94ad35084241f26b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/94ad35084241f26b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000301.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000301.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000302.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000302.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9a8c9825fcd2cedf_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9a8c9825fcd2cedf_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/708f79086b97b9d3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/708f79086b97b9d3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000303.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000303.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000304.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000304.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/77db89617a1ff978_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/77db89617a1ff978_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7eaa3d158dfb1c07_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7eaa3d158dfb1c07_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000305.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000305.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000307.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000307.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a116180c69f0c7e0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a116180c69f0c7e0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/724328e2b5c4f708_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/724328e2b5c4f708_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000308.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000308.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000309.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000309.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d37b7a6ea9a5b443_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d37b7a6ea9a5b443_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/84ec12148cf3aed3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/84ec12148cf3aed3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000310.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000310.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000311.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000311.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/37f9b3f6817c36c4_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/37f9b3f6817c36c4_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/47936cd052bff38a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/47936cd052bff38a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000312.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000312.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000313.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000313.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/bccdc0cac5d9cc7b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/bccdc0cac5d9cc7b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9693428fadd5788a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9693428fadd5788a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000314.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000314.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000316.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000316.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/26eb84fbcadc040f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/26eb84fbcadc040f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/82cce491f9c2c2f4_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/82cce491f9c2c2f4_0"}}