# User Preferences
- User prefers remote AI editor development workflow with local synchronization for running and testing Unity projects.
- User is interested in Unity development using cloud servers and cloud development with local sync workflow for running/testing.
- User prefers real-time/immediate synchronization between editor changes and local environment for development workflows.
- User is interested in using Cursor editor on headless Linux servers for remote development workflow.
- User's core requirement is to login to Cursor account on the cloud server for their development workflow.
- User uses Mac M1 locally with Ubuntu headless server and needs remote visualization solutions for development workflows.

# Remote Development Tools
- User is interested in Windsurf server deployment capabilities for remote development workflows.
- User wants to use code-server in conjunction with or as an alternative to Windsurf for remote development workflow.
`